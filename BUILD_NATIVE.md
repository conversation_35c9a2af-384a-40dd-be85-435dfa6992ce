# VoiceHype Native Binary Build Scripts

This directory contains scripts to build the native Rust binary for the VoiceHype paste functionality and copy it to the Flutter assets folder.

## Quick Start

### Cross-Platform (Recommended)
```bash
node build_native_binary.js --release
```

### Platform-Specific Scripts

**Windows:**
```cmd
build_native_binary.bat
```

**macOS/Linux:**
```bash
./build_native_binary.sh
```

## Requirements

- **Node.js** (for the main build script)
- **Rust** (cargo command must be available)

## What the Script Does

1. **Compiles** the Rust binary from `voicehype_paste_tool/`
2. **Copies** the binary to `assets/` folder with the correct name:
   - Windows: `assets/voice.exe`
   - macOS/Linux: `assets/voice`
3. **Makes executable** on Unix systems
4. **Ready** for Flutter app to use

## Options

- `--release` - Build in release mode (optimized, smaller binary)
- `--clean` - Clean target directory before building

## Examples

```bash
# Build in release mode (recommended for production)
node build_native_binary.js --release

# Build in debug mode
node build_native_binary.js

# Clean and build
node build_native_binary.js --release --clean
```

## Flutter Integration

The Flutter app's `PasteService` automatically detects the platform and loads the correct binary:

- **Windows**: Looks for `assets/voice.exe`
- **macOS/Linux**: Looks for `assets/voice`

## Troubleshooting

**"Cargo not found"**
- Install Rust: https://rustup.rs/

**"Node.js not found"**
- Install Node.js: https://nodejs.org/

**Binary not working**
- Try rebuilding with `--clean` flag
- Check that the binary has execute permissions on Unix systems

## Build Output

After successful build, you'll see:
```
✅ Build completed successfully!
📍 Binary location: assets/voice
🎉 Ready to use in Flutter app!
```

The binary is now ready to be packaged with your Flutter app.
