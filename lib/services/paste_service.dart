import 'dart:io';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class PasteService {
  static PasteService? _instance;
  static PasteService get instance => _instance ??= PasteService._();
  PasteService._();

  String? _binaryPath;

  Future<void> initialize() async {
    if (_binaryPath != null) return; // Already initialized

    _binaryPath = await _extractBinary();
  }

  Future<String> _extractBinary() async {
    // Get the correct asset path based on platform
    String assetPath = _getAssetPath();
    String fileName = _getFileName();

    try {
      // Load binary from assets
      final byteData = await rootBundle.load(assetPath);

      // Get temporary directory
      final tempDir = await getTemporaryDirectory();
      final binaryDir = Directory(
        path.join(tempDir.path, 'voicehype_binaries'),
      );
      await binaryDir.create(recursive: true);

      // Write binary to temp location
      final binaryFile = File(path.join(binaryDir.path, fileName));
      await binaryFile.writeAsBytes(byteData.buffer.asUint8List());

      // Make executable on Unix systems
      if (Platform.isLinux || Platform.isMacOS) {
        await Process.run('chmod', ['+x', binaryFile.path]);
      }

      return binaryFile.path;
    } catch (e) {
      throw Exception('Failed to extract paste binary: $e');
    }
  }

  String _getAssetPath() {
    if (Platform.isWindows) {
      return 'assets/voicehype-paste.exe';
    } else {
      return 'assets/voicehype-paste';
    }
  }

  String _getFileName() {
    if (Platform.isWindows) {
      return 'voicehype-paste.exe';
    } else {
      return 'voicehype-paste';
    }
  }

  /// Get current platform info for debugging
  String getPlatformInfo() {
    final platform = Platform.operatingSystem;
    final version = Platform.version;

    return 'Platform: $platform, Version: $version';
  }

  /// Execute paste command
  Future<bool> executePaste() async {
    await initialize();

    try {
      print('🔧 Using binary: $_binaryPath');
      print('📱 ${getPlatformInfo()}');

      final result = await Process.run(_binaryPath!, ['paste']);

      if (result.exitCode == 0) {
        print('✅ Paste executed successfully');
        print('Output: ${result.stdout}');
        return true;
      } else {
        print('❌ Paste failed with exit code: ${result.exitCode}');
        print('Error: ${result.stderr}');
        return false;
      }
    } catch (e) {
      print('❌ Exception executing paste: $e');
      return false;
    }
  }

  /// Paste specific text
  Future<bool> pasteText(String text) async {
    await initialize();

    try {
      print('🔧 Using binary: $_binaryPath');
      print('📱 ${getPlatformInfo()}');

      // Copy to clipboard
      await Clipboard.setData(ClipboardData(text: text));

      await executePaste();

      return true;
    } catch (e) {
      print('❌ Exception pasting text: $e');
      return false;
    }
  }
}
