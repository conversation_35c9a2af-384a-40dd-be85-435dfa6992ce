#!/bin/bash

# Cross-platform shell script to build native binary
# This is a wrapper around the Node.js script

echo "🚀 VoiceHype Native Binary Builder (Unix)"
echo "=========================================="

# Check if Node.js is available
if ! command -v node &> /dev/null; then
    echo "❌ Node.js not found. Please install Node.js: https://nodejs.org/"
    exit 1
fi

# Run the Node.js build script
node build_native_binary.js --release "$@"

if [ $? -ne 0 ]; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed successfully!"
