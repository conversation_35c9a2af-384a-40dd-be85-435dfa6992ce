use enigo::{
    Direction::{Press, Release, Click},
    Enigo, Key, Keyboard, Settings,
};
use std::env;
use std::process;
use std::thread;
use std::time::Duration;

fn main() {
    let args: Vec<String> = env::args().collect();
    
    // Parse command line arguments
    let (action, text) = parse_arguments(&args);
    
    match action.as_str() {
        "paste" => {
            if let Err(e) = simulate_paste() {
                eprintln!("Error: Failed to simulate paste: {}", e);
                process::exit(1);
            }
            println!("✅ Paste command executed successfully");
        },
        "paste-text" => {
            if text.is_empty() {
                eprintln!("Error: No text provided for paste-text command");
                process::exit(1);
            }
            if let Err(e) = simulate_paste_with_text(&text) {
                eprintln!("Error: Failed to paste text: {}", e);
                process::exit(1);
            }
            println!("✅ Text pasted successfully");
        },
        "help" | "--help" | "-h" => {
            print_help();
        },
        _ => {
            eprintln!("Error: Unknown command '{}'", action);
            print_help();
            process::exit(1);
        }
    }
}

fn parse_arguments(args: &[String]) -> (String, String) {
    if args.len() < 2 {
        return ("paste".to_string(), String::new());
    }
    
    let action = args[1].clone();
    let text = if args.len() > 2 {
        args[2..].join(" ")
    } else {
        String::new()
    };
    
    (action, text)
}

fn simulate_paste() -> Result<(), String> {
    // Small delay to ensure the calling application is ready
    thread::sleep(Duration::from_millis(100));
    
    let mut enigo = Enigo::new(&Settings::default())
        .map_err(|e| format!("Failed to initialize enigo: {}", e))?;
    
    // Simulate Ctrl+Shift+V (or Ctrl+V)
    #[cfg(target_os = "macos")]
    {
        enigo.key(Key::Meta, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Unicode('v'), Click).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Release).map_err(|e| format!("Key release failed: {}", e))?;
        enigo.key(Key::Meta, Release).map_err(|e| format!("Key release failed: {}", e))?;
    }
    
    #[cfg(not(target_os = "macos"))]
    {
        enigo.key(Key::Control, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Unicode('v'), Click).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Release).map_err(|e| format!("Key release failed: {}", e))?;
        enigo.key(Key::Control, Release).map_err(|e| format!("Key release failed: {}", e))?;
    }
    
    Ok(())
}

fn simulate_paste_with_text(_text: &str) -> Result<(), String> {
    // Small delay to ensure the calling application is ready
    thread::sleep(Duration::from_millis(100));
    
    let mut enigo = Enigo::new(&Settings::default())
        .map_err(|e| format!("Failed to initialize enigo: {}", e))?;
    
    // First, set clipboard with the provided text (if clipboard feature is enabled)
    #[cfg(feature = "clipboard")]
    {
        if let Err(e) = set_clipboard_text(text) {
            eprintln!("Warning: Could not set clipboard: {}", e);
        }
    }
    
    // Then simulate paste
    #[cfg(target_os = "macos")]
    {
        enigo.key(Key::Meta, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Unicode('v'), Click).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Release).map_err(|e| format!("Key release failed: {}", e))?;
        enigo.key(Key::Meta, Release).map_err(|e| format!("Key release failed: {}", e))?;
    }
    
    #[cfg(not(target_os = "macos"))]
    {
        enigo.key(Key::Control, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Press).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Unicode('v'), Click).map_err(|e| format!("Key press failed: {}", e))?;
        enigo.key(Key::Shift, Release).map_err(|e| format!("Key release failed: {}", e))?;
        enigo.key(Key::Control, Release).map_err(|e| format!("Key release failed: {}", e))?;
    }
    
    Ok(())
}

#[cfg(feature = "clipboard")]
fn set_clipboard_text(text: &str) -> Result<(), String> {
    #[cfg(target_os = "windows")]
    {
        use clipboard_win::{formats, set_clipboard};
        set_clipboard(formats::Unicode, text)
            .map_err(|e| format!("Windows clipboard error: {}", e))
    }
    
    #[cfg(target_os = "macos")]
    {
        use cocoa::appkit::NSPasteboard;
        use cocoa_foundation::base::nil;
        use cocoa_foundation::foundation::NSString;
        
        unsafe {
            let pasteboard = NSPasteboard::generalPasteboard(nil);
            pasteboard.clearContents();
            let ns_string = NSString::alloc(nil).init_str(text);
            let success = pasteboard.setString_forType(
                ns_string,
                cocoa::appkit::NSPasteboardTypeString
            );
            if success == cocoa_foundation::base::YES {
                Ok(())
            } else {
                Err("Failed to set macOS clipboard".to_string())
            }
        }
    }
    
    #[cfg(target_os = "linux")]
    {
        use arboard::Clipboard;
        let mut clipboard = Clipboard::new()
            .map_err(|e| format!("Failed to create clipboard: {}", e))?;
        clipboard.set_text(text)
            .map_err(|e| format!("Failed to set clipboard: {}", e))
    }
}

fn print_help() {
    println!(r#"
VoiceHype Paste Tool v0.1.0

USAGE:
    voicehype-paste [COMMAND] [TEXT]

COMMANDS:
    paste              Simulate Ctrl+Shift+V keystroke
    paste-text <TEXT>  Set clipboard to TEXT then paste
    help, --help, -h   Show this help message

EXAMPLES:
    voicehype-paste paste
    voicehype-paste paste-text "Hello World"

Made for VoiceHype - Voice-to-Text Transcription App
"#);
}