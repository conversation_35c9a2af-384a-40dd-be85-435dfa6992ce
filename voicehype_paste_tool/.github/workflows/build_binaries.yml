name: Build Cross-Platform Binaries
on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

jobs:
  build:
    strategy:
      matrix:
        include:
          # Linux x64
          - os: ubuntu-latest
            target: x86_64-unknown-linux-gnu
            output: voicehype-paste
            platform: linux-x64
          
          # Windows x64
          - os: windows-latest
            target: x86_64-pc-windows-msvc
            output: voicehype-paste.exe
            platform: windows-x64
          
          # Windows ARM64
          - os: windows-latest
            target: aarch64-pc-windows-msvc
            output: voicehype-paste.exe
            platform: windows-arm64
          
          # macOS Intel
          - os: macos-latest
            target: x86_64-apple-darwin
            output: voicehype-paste
            platform: macos-x64
          
          # macOS Apple Silicon
          - os: macos-latest
            target: aarch64-apple-darwin
            output: voicehype-paste
            platform: macos-arm64
    
    runs-on: ${{ matrix.os }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install Rust
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}
      
      - name: Install Linux dependencies
        if: matrix.os == 'ubuntu-latest'
        run: |
          sudo apt-get update
          sudo apt-get install -y libx11-dev libxtst-dev libxdo-dev pkg-config
      
      - name: Build binary
        run: cargo build --release --target ${{ matrix.target }}
      
      - name: Upload artifact
        uses: actions/upload-artifact@v4
        with:
          name: voicehype-paste-${{ matrix.platform }}
          path: target/${{ matrix.target }}/release/${{ matrix.output }}
          retention-days: 30

  # Optional: Create a release with all binaries
  release:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: binaries/
      
      - name: Display structure
        run: ls -la binaries/
      
      - name: Create Release
        if: startsWith(github.ref, 'refs/tags/')
        uses: softprops/action-gh-release@v1
        with:
          files: binaries/**/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}