[package]
name = "voicehype-paste"
version = "0.1.0"
edition = "2021"
authors = ["VoiceHype Team"]
description = "Simple paste utility for VoiceHype voice app"

[[bin]]
name = "voicehype-paste"
path = "src/main.rs"

[dependencies]
enigo = "0.2"

[profile.release]
strip = true          # Remove debug symbols
lto = true           # Link-time optimization
codegen-units = 1    # Better optimization
panic = "abort"      # Smaller binary size

# Platform-specific dependencies for clipboard access (optional)
[target.'cfg(target_os = "windows")'.dependencies]
clipboard-win = { version = "4.4", optional = true }

[target.'cfg(target_os = "macos")'.dependencies]
cocoa = { version = "0.24", optional = true }
cocoa-foundation = { version = "0.1", optional = true }

[target.'cfg(target_os = "linux")'.dependencies]
arboard = { version = "3.2", optional = true }

[features]
default = []
clipboard = ["clipboard-win", "cocoa", "cocoa-foundation", "arboard"]