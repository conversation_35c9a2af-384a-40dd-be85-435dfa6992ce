@echo off
REM Windows batch script to build native binary
REM This is a wrapper around the Node.js script

echo 🚀 VoiceHype Native Binary Builder (Windows)
echo =============================================

REM Check if Node.js is available
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js: https://nodejs.org/
    exit /b 1
)

REM Run the Node.js build script
node build_native_binary.js --release %*

if %errorlevel% neq 0 (
    echo ❌ Build failed
    exit /b 1
)

echo ✅ Build completed successfully!
pause
