#!/usr/bin/env node

/**
 * Cross-platform build script for VoiceHype native paste binary
 * Compiles the Rust binary and copies it to the appropriate assets folder
 * 
 * Usage: node build_native_binary.js [--release] [--clean]
 * 
 * Options:
 *   --release  Build in release mode (default: debug)
 *   --clean    Clean target directory before building
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

// Configuration
const RUST_PROJECT_DIR = 'voicehype_paste_tool';
const ASSETS_DIR = 'assets';
const BINARY_NAME = 'voicehype-paste';

// Parse command line arguments
const args = process.argv.slice(2);
const isRelease = args.includes('--release');
const shouldClean = args.includes('--clean');

// Platform detection
const platform = os.platform();
const arch = os.arch();

console.log('🚀 VoiceHype Native Binary Builder');
console.log('=====================================');
console.log(`Platform: ${platform}`);
console.log(`Architecture: ${arch}`);
console.log(`Build mode: ${isRelease ? 'release' : 'debug'}`);
console.log('');

/**
 * Execute a command and return the result
 */
function execCommand(command, options = {}) {
  try {
    console.log(`📋 Executing: ${command}`);
    const result = execSync(command, { 
      stdio: 'inherit', 
      cwd: options.cwd || process.cwd(),
      ...options 
    });
    return result;
  } catch (error) {
    console.error(`❌ Command failed: ${command}`);
    console.error(error.message);
    process.exit(1);
  }
}

/**
 * Get the target directory based on platform and architecture
 */
function getTargetInfo() {
  let targetTriple;
  let binaryExtension = '';
  let assetSubdir;

  switch (platform) {
    case 'win32':
      binaryExtension = '.exe';
      if (arch === 'x64') {
        targetTriple = 'x86_64-pc-windows-msvc';
        assetSubdir = 'windows-x64';
      } else if (arch === 'arm64') {
        targetTriple = 'aarch64-pc-windows-msvc';
        assetSubdir = 'windows-arm64';
      } else {
        throw new Error(`Unsupported Windows architecture: ${arch}`);
      }
      break;

    case 'darwin':
      if (arch === 'x64') {
        targetTriple = 'x86_64-apple-darwin';
        assetSubdir = 'macos-x64';
      } else if (arch === 'arm64') {
        targetTriple = 'aarch64-apple-darwin';
        assetSubdir = 'macos-arm64';
      } else {
        throw new Error(`Unsupported macOS architecture: ${arch}`);
      }
      break;

    case 'linux':
      if (arch === 'x64') {
        targetTriple = 'x86_64-unknown-linux-gnu';
        assetSubdir = 'linux-x64';
      } else if (arch === 'arm64') {
        targetTriple = 'aarch64-unknown-linux-gnu';
        assetSubdir = 'linux-arm64';
      } else {
        throw new Error(`Unsupported Linux architecture: ${arch}`);
      }
      break;

    default:
      throw new Error(`Unsupported platform: ${platform}`);
  }

  return {
    targetTriple,
    binaryExtension,
    assetSubdir,
    binaryName: BINARY_NAME + binaryExtension
  };
}

/**
 * Ensure directory exists
 */
function ensureDir(dirPath) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`📁 Created directory: ${dirPath}`);
  }
}

/**
 * Copy file with error handling
 */
function copyFile(src, dest) {
  try {
    fs.copyFileSync(src, dest);
    console.log(`📋 Copied: ${src} → ${dest}`);
  } catch (error) {
    console.error(`❌ Failed to copy file: ${error.message}`);
    process.exit(1);
  }
}

/**
 * Main build function
 */
function main() {
  // Check if Rust project exists
  if (!fs.existsSync(RUST_PROJECT_DIR)) {
    console.error(`❌ Rust project directory not found: ${RUST_PROJECT_DIR}`);
    process.exit(1);
  }

  // Check if cargo is available
  try {
    execSync('cargo --version', { stdio: 'pipe' });
  } catch (error) {
    console.error('❌ Cargo not found. Please install Rust: https://rustup.rs/');
    process.exit(1);
  }

  const targetInfo = getTargetInfo();
  console.log(`🎯 Target: ${targetInfo.targetTriple}`);
  console.log(`📦 Binary: ${targetInfo.binaryName}`);
  console.log(`📁 Asset subdirectory: ${targetInfo.assetSubdir}`);
  console.log('');

  // Clean if requested
  if (shouldClean) {
    console.log('🧹 Cleaning target directory...');
    execCommand('cargo clean', { cwd: RUST_PROJECT_DIR });
  }

  // Add target if not already installed
  console.log('🔧 Ensuring target is installed...');
  execCommand(`rustup target add ${targetInfo.targetTriple}`);

  // Build the binary
  console.log('🔨 Building Rust binary...');
  const buildMode = isRelease ? 'release' : 'debug';
  const buildCommand = `cargo build --target ${targetInfo.targetTriple}${isRelease ? ' --release' : ''}`;
  execCommand(buildCommand, { cwd: RUST_PROJECT_DIR });

  // Determine source binary path
  const sourceBinaryPath = path.join(
    RUST_PROJECT_DIR,
    'target',
    targetInfo.targetTriple,
    buildMode,
    targetInfo.binaryName
  );

  // Check if binary was created
  if (!fs.existsSync(sourceBinaryPath)) {
    console.error(`❌ Binary not found at: ${sourceBinaryPath}`);
    process.exit(1);
  }

  // Create assets directory structure
  const assetBinaryDir = path.join(ASSETS_DIR, 'binaries', targetInfo.assetSubdir);
  ensureDir(assetBinaryDir);

  // Copy binary to assets
  const destBinaryPath = path.join(assetBinaryDir, targetInfo.binaryName);
  copyFile(sourceBinaryPath, destBinaryPath);

  // Make executable on Unix systems
  if (platform !== 'win32') {
    try {
      fs.chmodSync(destBinaryPath, 0o755);
      console.log(`🔐 Made executable: ${destBinaryPath}`);
    } catch (error) {
      console.warn(`⚠️  Could not make executable: ${error.message}`);
    }
  }

  console.log('');
  console.log('✅ Build completed successfully!');
  console.log(`📍 Binary location: ${destBinaryPath}`);
  console.log('');
  console.log('🎉 Ready to use in Flutter app!');
}

// Run the main function
if (require.main === module) {
  main();
}

module.exports = { main, getTargetInfo };
